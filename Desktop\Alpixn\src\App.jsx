import {useState} from "react";
import "./App.css";

function App() {
  const [activeTab, setActiveTab] = useState("dashboard");

  // Sample data
  const stats = [
    {
      title: "Website Traffic",
      value: "12,000",
      change: "+5%",
      color: "bg-blue-600",
    },
    {
      title: "Users Generated",
      value: "3,000",
      change: "+12%",
      color: "bg-gray-600",
    },
    {title: "Top Pages", value: "780", change: "+8%", color: "bg-gray-600"},
    {
      title: "Pending Requests",
      value: "5",
      change: "-2%",
      color: "bg-gray-600",
    },
  ];

  const services = [
    {
      title: "Google Ads",
      description:
        "Boost your business with targeted ads. Reach the right audience.",
      image: "🎯",
    },
    {
      title: "Facebook Ads",
      description: "Boost engagement with effective social media advertising.",
      image: "📱",
    },
    {
      title: "SEO Services",
      description: "Get found from Google with our SEO services.",
      image: "🔍",
    },
  ];

  const leads = [
    {
      name: "<PERSON><PERSON>",
      service: "SEO",
      budget: "40k - 50k",
      status: "Looking for to sharing...",
      date: "June 12, 2025",
      action: "Generate Quotation",
    },
    {
      name: "Bobby",
      service: "Google Ads",
      budget: "50k - 60k",
      status: "Profile",
      date: "Profile",
      action: "Generate Quotation",
    },
    {
      name: "Atharv",
      service: "SEO",
      budget: "52k - 60k",
      status: "Profile",
      date: "Profile",
      action: "Generate Quotation",
    },
    {
      name: "Reeth",
      service: "SEO",
      budget: "40k - 50k",
      status: "Profile",
      date: "Profile",
      action: "Generate Quotation",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">A</span>
            </div>
            <span className="text-xl font-bold text-gray-800">ALPIXN</span>
          </div>
        </div>

        <nav className="mt-6">
          <div className="px-6 space-y-2">
            <a
              href="#"
              className="flex items-center space-x-3 text-blue-600 bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>📊</span>
              <span className="font-medium">Dashboard</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>⚙️</span>
              <span>Services</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>💰</span>
              <span>Pricing Tables</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>👥</span>
              <span>Leads</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>📝</span>
              <span>Blog</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>🔍</span>
              <span>SEO</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>💬</span>
              <span>Testimonials</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>📁</span>
              <span>Portfolio</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>📱</span>
              <span>Media</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg"
            >
              <span>👤</span>
              <span>Manage Users</span>
            </a>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg
                  className="absolute left-3 top-2.5 w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Dark/Light Mode Toggle */}
              <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </button>

              {/* Notifications */}
              <button className="relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-5 5v-5zM10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.061L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"
                  />
                </svg>
                {/* Notification Badge */}
                <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Profile Section */}
              <div className="flex items-center space-x-3">
                <span className="text-gray-700 font-medium">Atharv Singh</span>
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">A</span>
                </div>
                <button className="p-1 text-gray-600 hover:text-gray-800">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
              </div>

              {/* Settings */}
              <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6 overflow-y-auto h-full">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              WELCOME Atharv!
            </h1>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className={`${stat.color} text-white p-6 rounded-lg relative overflow-hidden`}
                >
                  <div className="relative z-10">
                    <p className="text-sm opacity-90 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold mb-1">{stat.value}</p>
                    <p className="text-sm opacity-75">{stat.change}</p>
                  </div>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Services Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800 flex items-center">
                <span className="mr-2">⚙️</span>
                Services
              </h2>
              <button className="text-blue-600 border border-blue-600 px-4 py-2 rounded-lg hover:bg-blue-50">
                Manage Services
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-white p-6 rounded-lg shadow-sm border"
                >
                  <div className="text-4xl mb-4">{service.image}</div>
                  <h3 className="font-bold text-gray-800 mb-2">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {service.description}
                  </p>
                  <button className="w-full border-2 border-dashed border-gray-300 py-3 rounded-lg text-gray-500 hover:border-blue-300 hover:text-blue-600">
                    + Add New Service
                  </button>
                </div>
              ))}
            </div>

            {/* Service Promotion Banner */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-lg flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-2">
                  "Drive more traffic to your website with expert
                </h3>
                <h3 className="text-xl font-bold mb-2">
                  Search Engine Optimization
                </h3>
                <h3 className="text-xl font-bold">strategies."</h3>
              </div>
              <div className="hidden md:block">
                <img
                  src="/api/placeholder/200/150"
                  alt="SEO Expert"
                  className="rounded-lg"
                />
              </div>
            </div>
          </div>

          {/* Leads and Quotations Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800 flex items-center">
                <span className="mr-2">📊</span>
                Leads and Quotations
              </h2>
              <button className="text-blue-600 hover:text-blue-800">
                View All Quotations →
              </button>
            </div>

            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-blue-600 text-white">
                    <tr>
                      <th className="px-6 py-3 text-left text-sm font-medium">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-sm font-medium">
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-sm font-medium">
                        Budget
                      </th>
                      <th className="px-6 py-3 text-left text-sm font-medium">
                        Message
                      </th>
                      <th className="px-6 py-3 text-left text-sm font-medium">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-sm font-medium">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {leads.map((lead, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <input type="checkbox" className="mr-3" />
                            <span className="text-sm font-medium text-gray-900">
                              {lead.name}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600">
                          {lead.service}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600">
                          {lead.budget}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600">
                          {lead.status}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600">
                          {lead.date}
                        </td>
                        <td className="px-6 py-4">
                          <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                            {lead.action}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="px-6 py-4 bg-gray-50 border-t flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Get connected from leads generated to Earn CRM
                </p>
                <div className="flex space-x-2">
                  <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Export to Excel
                  </button>
                  <button className="border border-gray-300 px-4 py-2 rounded hover:bg-gray-50">
                    View More
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Blog Section */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-orange-400 to-orange-600 text-white p-6 rounded-lg relative overflow-hidden">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold flex items-center">
                    <span className="mr-2">📝</span>
                    Blogs
                  </h2>
                  <button className="bg-white bg-opacity-20 px-4 py-2 rounded hover:bg-opacity-30">
                    All Blogs
                  </button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <button className="bg-white bg-opacity-20 px-4 py-2 rounded mr-4 hover:bg-opacity-30">
                      + Add/Edit Blogs
                    </button>
                    <span className="text-sm opacity-90">SEO Settings</span>
                  </div>
                  <div>
                    <span className="text-sm opacity-90">Blog Title</span>
                    <br />
                    <span className="text-sm opacity-90">Meta Title</span>
                  </div>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
            </div>
          </div>

          {/* Media Section */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-lg relative overflow-hidden">
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold flex items-center">
                    <span className="mr-2">📱</span>
                    Media
                  </h2>
                  <button className="bg-white bg-opacity-20 px-4 py-2 rounded hover:bg-opacity-30">
                    All Media
                  </button>
                </div>
                <div className="flex items-center space-x-4">
                  <input
                    type="text"
                    placeholder="Search media..."
                    className="flex-1 px-4 py-2 rounded bg-white bg-opacity-20 placeholder-white placeholder-opacity-70 text-white"
                  />
                  <button className="bg-gray-800 px-4 py-2 rounded hover:bg-gray-700">
                    Filter
                  </button>
                  <button className="bg-white text-purple-600 px-4 py-2 rounded hover:bg-gray-100">
                    Upload
                  </button>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
            </div>
          </div>

          {/* Bottom Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Testimonials and Case Studies */}
            <div className="bg-gray-800 text-white p-6 rounded-lg relative overflow-hidden">
              <div className="relative z-10">
                <h2 className="text-xl font-bold mb-4 flex items-center">
                  <span className="mr-2">💬</span>
                  Testimonials and Case Studies
                </h2>
                <p className="text-gray-300 text-sm">
                  Showcase your success stories and client testimonials to build
                  trust and credibility.
                </p>
              </div>
              <div className="absolute bottom-0 right-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -mr-12 -mb-12"></div>
            </div>

            {/* Media Gallery */}
            <div className="bg-gradient-to-br from-pink-500 to-purple-600 text-white p-6 rounded-lg relative overflow-hidden">
              <div className="relative z-10">
                <h2 className="text-xl font-bold mb-4 flex items-center">
                  <span className="mr-2">🎨</span>
                  Media
                </h2>
                <p className="text-white text-opacity-90 text-sm">
                  Manage your media files, images, and digital assets in one
                  place.
                </p>
              </div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-white bg-opacity-20 rounded-full -mr-12 -mt-12"></div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
